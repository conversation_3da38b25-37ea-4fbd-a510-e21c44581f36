export interface SupportToken {
  symbol: string;
  address?: string;
  isNative: boolean;
  walletAddress?: string;
  tokenAddress?: string;
  decimal?: number;
}

export interface PaymentOrder {
  orderId: string;
  orderValue: number | string;
  currency: string;
  currencySymbol?: string;
  merchantName: string;
  merchantSolanaAddress: string;
  merchantLogo?: string;
  supportTokenList: SupportToken[];
  secondVerification: boolean;
  paymentStatus: string;
  defaultPaymentToken: string;
  transaction_total?: number;
  transaction_limit?: number;
}

export interface PaymentInfo {
  payTokenSymbol: string;
  payTokenAddress?: string;
  payTokenAmount: string;
  tokenPrice?: string;
}

export interface PaymentReceipt {
  signature: string;
  toAddress: string;
  merchantName: string;
  tokenSymbol: string;
  fees: string;
  timestamp: number;
  amount: string;
  transaction_total?: number;
  transaction_limit?: number;
  orderValue?: string | number;
  orderId?: string;
}

export interface UpdateOrderInfo {
  transactionId: string;
  cryptoSymbol: string;
  cryptoAmount: number;
  cryptoTxHash: string;
  collectWallet: string;
  paymentStatus: string;
  payerWallet: string;
  appUserId?: number;
  pushAppId?: string;
  cryptoCurrencyRate?: number | string;
}

export interface CoinPriceInfo {
  tokenSymbol: string;
}

// 添加 ICP 相关的类型定义
interface ICPTransferResult {
  Ok?: bigint;
  Err?: string;
}

// 添加 CoinCalculator 相关的类型定义

export interface Token {
  tokenAddress?: string;
  isNative: boolean;
  symbol: string;
  decimal: number;
}

export interface Order {
  currency: string;
  defaultPaymentToken: string;
  merchantName: string;
  merchantSolanaAddress: string;
  orderId: string;
  orderValue: string;
  paymentStatus: 'new' | 'success' | 'faile' | 'pending';
  secondVerification: boolean;
  supportTokenList: Token[];
  transaction_limit?: number;
  transaction_total?: number;
  createTime: number;
  paymentResult?: {
    symbol: string;
    amount: string;
    txHash: string;
    gasFee: string;
  };
}

export interface CoinCalculator {
  tokenPrice?: string;
  payTokenAmount: string;
  payTokenSymbol: string;
  payTokenDecimal: number;
}
