import * as React from 'react';
import {
  Platform,
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Image,
} from 'react-native';

import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { TokenList } from '../../UI/SolanaWallet/Token/TokenList';
import { useScannerProvider } from '../../../hooks/useScannerProvider';
import { primaryBackgroundColor } from '../../../theme/default';
import { SwitchWalletLoadingModal } from '../../UI/SolanaWallet/SwitchWalletLoadingModal';
import { SwitchWalletModal } from '../../UI/SolanaWallet/SwitchWalletModal';
import { RootStackParamList } from '../../Nav/routes';
import { getOrderById, getPaynowTransactionInfo } from '../../../api/backend/order';
import Toast from 'react-native-toast-message';
import { useTranslation } from 'react-i18next';
import { useNavigation, useIsFocused } from '@react-navigation/native';
import { useNdefOrderProvider } from '../../../hooks/useNdefOrderProvider';
import { useJPush } from '../../../hooks/useJPush';
import { useAccountProvider } from '../../../hooks/useAccountProvider';
import { useAuthProvider } from '../../../hooks/useAuthProvider';
import WalletHeader from './WalletHeader';
import ArrowRight from '../../../assets/icons/arrow_right.svg';
import SendIcon from '../../../assets/icons/send-1.svg';
import ReceiveIcon from '../../../assets/icons/receive-1.svg';
import BottomSheet from '../../UI/BottomSheet/BottomSheet';
import { UserToken } from '../../../db/models/userTokenModel';
import SendView from '../Send';
import ReceiveView from '../Receive';
import SendIcrc1View from '../Send/icrc1';
import LoadingModal from '../../UI/Common/LoadingModal';
import { isLikelyPayNowQR, parseEMVCoQRCode } from '../../../utils';

export type WalletScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Main'
>;

type SendNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Send'>;
type SendIcrc1StackNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SendIcrc1'
>;
type ReceiveNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Receive'
>;

type Props = {
  navigation: WalletScreenNavigationProp;
};

function Wallet({ navigation }: Readonly<Props>) {
  const isFocused = useIsFocused();
  const { codeResult, setCodeResult } = useScannerProvider();
  const { t } = useTranslation();
  const { userInfo } = useAccountProvider();
  const { authToken } = useAuthProvider();
  const navigation2 =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const { orderInfo, setOrderInfo } = useNdefOrderProvider();
  const [isLoadingOrder, setIsLoadingOrder] = React.useState(false);

  // Send and Receive states
  const [isSendListBottomSheetVisible, setIsSendListBottomSheetVisible] =
    React.useState(false);
  const [isSendBottomSheetVisible, setIsSendBottomSheetVisible] =
    React.useState(false);
  const [isReceiveListBottomSheetVisible, setIsReceiveListBottomSheetVisible] =
    React.useState(false);
  const [isReceiveBottomSheetVisible, setIsReceiveBottomSheetVisible] =
    React.useState(false);
  const [currentTokenInfo, setCurrentTokenInfo] =
    React.useState<UserToken | null>(null);

  React.useEffect(() => {
    const handleQrCodeResult = async () => {
      console.log('wallet view scan code result', codeResult);
      // 正则匹配 codeResult，合法格式：${host}/webpay/order_id/${orderId}
      const regex = /^https?:\/\/[^/]+\/webpay\/order_id\/([^/]+)$/;

      // paynow qr code
      let payNowQRExtendId = '';

      if (codeResult) {
        const codeMatch = codeResult.codeResult.match(regex);
        console.log('codeMatch', codeMatch);
        let orderId;
        if (codeMatch && codeMatch.length > 1) {
          console.log('get orderId from new codeResult', codeMatch[1]);
          orderId = codeMatch[1];
        } else if (isLikelyPayNowQR(codeResult.codeResult)) {
          // paynow qr code
          const qrData = parseEMVCoQRCode(codeResult.codeResult);
          console.log('paynow qrData', qrData);
          payNowQRExtendId = 'UNIWEB1100007711044927489KnGa7'// TODO: qrData['62'];
        } else {
          console.log('get orderId from old codeResult', codeResult.codeResult);
          orderId = codeResult.codeResult;
        }

        if (!authToken) {
          console.warn('get token failed, authToken is null');
          Toast.show({
            type: 'info',
            text1: t('QRCodeTile'),
            text2: t('getAuthTokenFailed'),
            position: Platform.OS === 'android' ? 'top' : 'bottom',
          });
          return;
        }
        try {
          // 显示loading
          setIsLoadingOrder(true);

          let orderInfo;

          if (payNowQRExtendId) {
            console.log('开始查询订单信息...', payNowQRExtendId);
            orderInfo = await getPaynowTransactionInfo({ externalTransactionId: payNowQRExtendId });
            console.log('get orderInfo from paynow:', JSON.stringify(orderInfo));
            if (orderInfo) {
              navigation.navigate('Payment', { paynowId: payNowQRExtendId });
            } else {
              Toast.show({
                type: 'info',
                text1: t('QRCodeTile'),
                text2: t('unsupportedQRCode'),
                position: Platform.OS === 'android' ? 'top' : 'bottom',
              });
            }
          } else {
            console.log('开始查询订单信息...', orderId);
            orderInfo = await getOrderById(orderId as string);
            console.log('get orderInfo from orderId:', JSON.stringify(orderInfo));
            if (orderInfo) {
              navigation.navigate('Payment', { orderId: orderInfo.orderId as string });
            } else {
              Toast.show({
                type: 'info',
                text1: t('QRCodeTile'),
                text2: t('unsupportedQRCode'),
                position: Platform.OS === 'android' ? 'top' : 'bottom',
              });
            }
          }

          setCodeResult(undefined);



        } catch (error) {
          console.error('查询订单信息失败:', error);
          Toast.show({
            type: 'error',
            text1: t('QRCodeTile'),
            text2: t('networkError') || '网络错误，请重试',
            position: Platform.OS === 'android' ? 'top' : 'bottom',
          });
        } finally {
          // 隐藏loading
          setIsLoadingOrder(false);
        }
      }
    };
    if (isFocused) {
      handleQrCodeResult();
    }
  }, [codeResult]);

  React.useEffect(() => {
    const handleNfcOrder = async () => {
      if (orderInfo !== undefined) {
        if (orderInfo.orderNo != 'error') {
          if (!authToken) {
            console.warn('get token failed, authToken is null');
            return;
          }

          try {
            // 显示loading
            setIsLoadingOrder(true);
            console.log('开始查询NFC订单信息...');

            const uniWebOrderInfo = await getOrderById(
              orderInfo.orderNo,
            );

            if (uniWebOrderInfo != null) {
              navigation.navigate('Payment', { orderId: orderInfo.orderNo });
              setOrderInfo(undefined);
            }
          } catch (error) {
            console.error('查询NFC订单信息失败:', error);
            Toast.show({
              type: 'error',
              text1: t('tapToPay'),
              text2: t('networkError') || '网络错误，请重试',
              position: Platform.OS === 'android' ? 'top' : 'bottom',
            });
          } finally {
            // 隐藏loading
            setIsLoadingOrder(false);
          }
        }
      }
    };
    if (isFocused) {
      handleNfcOrder();
    }
  }, [orderInfo]);

  const { useJPushInit } = useJPush();

  React.useEffect(() => {
    //userInfo?.principalId
    if (userInfo?.principalId) {
      useJPushInit(userInfo?.principalId);
    }
  }, []);
  return (
    // <ImageBackground source={require('../../../assets/73679001.png')} resizeMode='repeat' style={{
    //     flex: 1,
    //     justifyContent: 'flex-start',
    //     alignItems: 'center',
    // }}>
    <View
      style={{
        flex: 1,
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: primaryBackgroundColor,
        paddingBottom: 100,
        paddingTop: 50, // 添加顶部空间给 WalletHeader
      }}>
      <WalletHeader
        onSendPress={() => setIsSendListBottomSheetVisible(true)}
        onReceivePress={() => setIsReceiveListBottomSheetVisible(true)}
      />

      <View style={styles.cardWrapper}>
        <TouchableOpacity
          style={styles.TipBox}
          onPress={() => {
            navigation2.navigate('CardList');
          }}>
          <Text style={styles.TipText}>
            Connect 💳 card to receive an 🎁 airdrop{' '}
          </Text>
          <ArrowRight
            width={18}
            height={18}
            color="#fff"
            style={{
              position: 'relative',
              zIndex: 3,
            }}
          />
          {/* background image */}
          <Image
            source={require('../../../assets/images/tip_box.png')}
            style={styles.TipBoxImage}
          />
        </TouchableOpacity>
      </View>

      <SwitchWalletModal />
      <SwitchWalletLoadingModal />
      <LoadingModal visible={isLoadingOrder} />

      {/* Send list Bottom Sheet */}
      <BottomSheet
        isVisible={isSendListBottomSheetVisible && isFocused}
        onClose={() => setIsSendListBottomSheetVisible(false)}
        defaultPosition="50%"
        showBackButton={false}
        title="Send"
        containsVirtualizedList={true}>
        <TokenList
          onItemPress={item => {
            const tokenInfo = item as UserToken;
            setCurrentTokenInfo(tokenInfo);
            setIsSendBottomSheetVisible(true);
          }}
          type="select"
        />
      </BottomSheet>

      {/* Receive list Bottom Sheet */}
      <BottomSheet
        isVisible={isReceiveListBottomSheetVisible && isFocused}
        onClose={() => setIsReceiveListBottomSheetVisible(false)}
        defaultPosition="50%"
        showBackButton={false}
        title="Receive"
        containsVirtualizedList={true}>
        <TokenList
          onItemPress={item => {
            const tokenInfo = item as UserToken;
            setCurrentTokenInfo(tokenInfo);
            setIsReceiveBottomSheetVisible(true);
          }}
          type="select"
        />
      </BottomSheet>

      {/* send bottom sheet */}
      {currentTokenInfo && (
        <BottomSheet
          isVisible={isSendBottomSheetVisible && isFocused}
          onClose={() => setIsSendBottomSheetVisible(false)}
          defaultPosition="90%"
          title={`Send ${currentTokenInfo.name}`}>
          {(currentTokenInfo as UserToken).type == 'icrc1' ? (
            <SendIcrc1View
              route={{ params: { tokenInfo: currentTokenInfo } } as any}
              navigation={navigation as unknown as SendIcrc1StackNavigationProp}
            />
          ) : (
            <SendView
              route={{ params: { tokenInfo: currentTokenInfo } } as any}
              navigation={navigation as unknown as SendNavigationProp}
            />
          )}
        </BottomSheet>
      )}

      {/* receive bottom sheet */}
      {currentTokenInfo && (
        <BottomSheet
          isVisible={isReceiveBottomSheetVisible && isFocused}
          onClose={() => setIsReceiveBottomSheetVisible(false)}
          defaultPosition="90%"
          title={`Receive ${currentTokenInfo.name}`}>
          <ReceiveView
            route={{ params: { tokenInfo: currentTokenInfo } } as any}
            navigation={navigation as unknown as ReceiveNavigationProp}
          />
        </BottomSheet>
      )}
    </View>

    // </ImageBackground>
  );
}

const styles = StyleSheet.create({
  cardWrapper: {
    width: '100%',
    marginBottom: 15,
    backgroundColor: primaryBackgroundColor,
    marginTop: -32,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  TipBox: {
    position: 'relative',
    paddingTop: 40,
    paddingBottom: 50,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
  },
  TipBoxImage: {
    position: 'absolute',
    top: 60,
    left: '10%',
    width: '100%',
    zIndex: 1,
  },
  TipText: {
    position: 'relative',
    zIndex: 2,
    color: '#fff',
    fontSize: 14,
    fontWeight: 600,
  },
});

export default Wallet;
