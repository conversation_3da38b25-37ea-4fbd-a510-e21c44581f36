import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { usePayment } from '../../../hooks/usePayment';
import {
  ActivityIndicator,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  primaryBackgroundColor,
  primaryColor,
  primaryColor2,
  primaryBackgroundColor2,
} from '../../../theme/default';
import { CoinCalculator, PaymentReceipt, SupportToken } from '../../../types/order';
import { useSolanaOwnerTokenAccountsProvider } from '../../../hooks/useSolana/useSolanaOwnerTokenAccounts';
import { UserToken } from '../../../db/models/userTokenModel';
import { calTokenValue, parseUnits, parseValue } from '../../../utils';
import { useAccountProvider } from '../../../hooks/useAccountProvider';
import { useSolanaTransaction } from '../../../hooks/useSolana/useSolanaTransaction';
import { LAMPORTS_PER_SOL, Transaction } from '@solana/web3.js';
import { useSolanaClientProvider } from '../../../hooks/useSolana/useSolanaClientProvider';
import { useTranslation } from 'react-i18next';
import { PasskeyAuthFailed } from '../../../types/errors';
import { usePasskeyAuth } from '../../../hooks/usePasskeyAuth';
import { useTokenPriceProvider } from '../../../hooks/useTokenPriceProvider';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../Nav/routes';
import tw from 'twrnc';
import ArrowDown from '../../../assets/icons/arrow_down.svg';
import FaceIcon from '../../../assets/icons/face.svg';
import { TokenLogo } from '../TokenLogo';
import { useSwitchTokenModalProvider } from '../../../hooks/useSwitchTokenModalProvider';
import { SwitchTokenModal } from '../SolanaWallet/Token/SwitchToken';
import Toast from 'react-native-toast-message';
import BigNumber from 'bignumber.js';
import { useIcrc1TokenTransfer } from '../../../hooks/useICP/useIcrc1TokenTransfer';
import { MMKV } from 'react-native-mmkv';
import { useKYC } from '../../../hooks/useKYC';
import { PaymentCardLayout } from './PaymentCardLayout';
import { getPaynowTransactionInfo } from '../../../api/backend/order';
import { Order } from '../../../types/order';
import { formatUnits } from '../../../utils';

export const storage = new MMKV();
type PaymentcardProp = {
  externalTransactionId: string;
  onReceipt: (receipt: PaymentReceipt) => void;
};


export default function PaymentCardPaynow({
  externalTransactionId,
  onReceipt,
}: Readonly<PaymentcardProp>) {
  const {
    getPaymentInfo,
  } = usePayment();
  const [orderLoading, setOrderLoading] = useState<boolean>(false);
  const [orderInfo, setOrderInfo] = useState<Order>();
  const getOrderInfoById = async function (externalTransactionId: string) {
    setOrderLoading(true);
    const orderInfo = await getPaynowTransactionInfo({ externalTransactionId })
    if (!orderInfo) {
      console.log('orderInfo is null');
      return null;
    }
    console.log('got order info:', JSON.stringify(orderInfo))
    setOrderInfo(orderInfo);
    setOrderLoading(false);
    return orderInfo as (Order);
  }
  const orderId = useMemo(() => orderInfo?.orderId, [orderInfo])
  const [paymentInfo, setPaymentInfo] = useState<CoinCalculator>();
  const [currentPaymentToken, setCurrentPaymentToken] =
    useState<SupportToken>();
  const [currentPaymentTokenList, setCurrentPaymentTokenList] = useState<
    SupportToken[]
  >([]);
  const [currentUserToken, setCurrentUserToken] = useState<UserToken>();
  const [currentUserTokenList, setCurrentUserTokenList] = useState<UserToken[]>(
    [],
  );
  const { userInfo } = useAccountProvider();
  const [rawTx, setRawTx] = useState<Transaction>();
  const [rawFee, setRawFee] = useState<string>('');
  const [feeValue, setFeeValue] = useState<string>('');
  const { solanaClient } = useSolanaClientProvider();
  const [paying, setPaying] = useState<boolean>(false);
  const { getTokenPrice, tokenPrices } = useTokenPriceProvider();
  const { passkeyAuth } = usePasskeyAuth();
  const [paymentInfoLoading, setPaymentInfoLoading] = useState<boolean>(true);

  const { t } = useTranslation();
  const { toggleModal } = useSwitchTokenModalProvider();
  const { loading, icrc1TokenTransfer } = useIcrc1TokenTransfer();
  const { userTokens, getTokenAccounts } = useSolanaOwnerTokenAccountsProvider();
  const { createTransferSOLTx, createTransferSPLTx, signTx, sendRawTx } =
    useSolanaTransaction();

  const stringifyWithBigInt = (data: unknown): string => {
    return JSON.stringify(data, (_key, value) =>
      typeof value === 'bigint' ? value.toString() : value,
    );
  };
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const { launchSNSMobileSDK, getToken } = useKYC();

  const startKYC = async () => {
    const token = await getToken();
    if (token) {
      launchSNSMobileSDK(() => {
        console.log('kyc callback: start reload order info');
        getOrderInfoById(externalTransactionId);
      });
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <Text style={{ color: primaryColor2, fontSize: 18, fontWeight: '600' }}>
            {t('payTitle')}
          </Text>
          {(orderLoading || paymentInfoLoading) && (
            <ActivityIndicator
              size="small"
              color={primaryColor}
              style={{ marginLeft: 6 }}></ActivityIndicator>
          )}
        </View>
      ),
    });
  }, [orderLoading, paymentInfoLoading]);

  useEffect(() => {
    getTokenAccounts();
    getOrderInfoById(externalTransactionId);
  }, [externalTransactionId]);

  useEffect(() => {
    if (currentPaymentToken != undefined) {
      const userToken = userTokens.find(item =>
        currentPaymentToken.isNative
          ? item.symbol.toLowerCase() ===
          currentPaymentToken.symbol.toLowerCase()
          : item.symbol.toLowerCase() ===
          currentPaymentToken.symbol.toLowerCase(),
      );

      setCurrentUserToken(userToken);
    }
  }, [userTokens, currentPaymentToken]);

  const handleSelectToken = (value: UserToken) => {
    const selectPaymentToken = currentPaymentTokenList.find(item => {
      if (item.isNative) {
        return item.symbol.toLowerCase() === value.symbol.toLowerCase();
      } else {
        return (
          item.symbol.toLowerCase() === value.symbol.toLowerCase() &&
          item.address?.toLowerCase() === value.address.toLowerCase()
        );
      }
    });

    if (selectPaymentToken) {
      setCurrentPaymentToken(selectPaymentToken);
    } else {
      Toast.show({
        type: 'error',
        text1: t('paymentTitle'),
        text2: t('invalidToken'),
        position: Platform.OS === 'android' ? 'top' : 'bottom',
      });
    }
  };

  useEffect(() => {
    if (currentPaymentTokenList.length > 0) {
      const tokens = userTokens.filter(item => {
        const pToken = currentPaymentTokenList.find(pItem => {
          if (pItem.isNative) {
            return pItem.symbol.toLowerCase() === item.symbol.toLowerCase();
          } else {
            return pItem.symbol.toLowerCase() === item.symbol.toLowerCase();
          }
        });
        return pToken !== undefined;
      });

      setCurrentUserTokenList(tokens);
    }
  }, [userTokens, currentPaymentTokenList]);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () =>
        currentUserTokenList.length > 0 && currentUserToken ? (
          <TouchableOpacity
            onPress={toggleModal}
            style={tw.style(
              `flex flex-row items-center gap-2 bg-[${primaryBackgroundColor2}] p-2 rounded-xl`,
            )}>
            <TokenLogo
              tokenName={currentUserToken.name}
              logoUri={currentUserToken.logo}
              size={20}
              borderRadius={20}
              backgroundColor="blue"
              textFontSize={12}
            />
            <Text style={tw.style(`text-[${primaryColor2}]`)}>
              {currentUserToken.symbol}
            </Text>
            <ArrowDown color="#AAA" width={20} height={20} />
          </TouchableOpacity>
        ) : undefined,
    });
  }, [currentUserTokenList, currentUserToken, navigation]);

  useEffect(() => {
    if (!orderLoading && orderInfo != undefined) {
      setCurrentPaymentTokenList(orderInfo.supportTokenList);
      const vUSDToken = orderInfo.supportTokenList.find(
        token => token.symbol.toLowerCase() === 'vusd',
      );

      setCurrentPaymentToken(vUSDToken || orderInfo.supportTokenList[0]);
    }
  }, [orderLoading, orderInfo]);

  useEffect(() => {
    if (orderId) {
      const getCurrentPaymentInfo = async () => {
        setPaymentInfoLoading(true);

        const info = await getPaymentInfo(
          orderId,
          currentPaymentToken!.symbol,
          currentPaymentToken?.tokenAddress,
        );
        if (info == null) {
          Toast.show({
            type: 'error',
            text1: t('paymentTitle'),
            text2: t('getPaymentInfoFailed'),
            position: Platform.OS === 'android' ? 'top' : 'bottom',
          });
        } else {
          setPaymentInfo(info);
        }
        setPaymentInfoLoading(false);
      };

      if (currentPaymentToken != undefined) {
        getCurrentPaymentInfo();
      }

      const interval = setInterval(() => {
        if (currentPaymentToken != undefined) {
          console.log('refresh payment info....2');
          getCurrentPaymentInfo();
        }
      }, 20 * 1000);

      return () => {
        clearInterval(interval);
      };
    }

  }, [currentPaymentToken, orderId, userTokens]);

  useEffect(() => {
    const handleFeeValue = async () => {
      if (rawFee == '') {
        setFeeValue('SGD 0');
        return;
      }

      const currentTokenPrice = tokenPrices.find(
        item =>
          item.symbol.toLowerCase() ===
          currentPaymentToken?.symbol.toLowerCase(),
      );

      if (currentTokenPrice) {
        const feeValue = calTokenValue(
          parseValue(rawFee, 9),
          currentTokenPrice.price,
        );

        setFeeValue(feeValue);
      } else {
        const tokenPrice = await getTokenPrice(
          currentPaymentToken?.symbol ?? 'SOL',
        );

        const feeValue = calTokenValue(parseValue(rawFee, 9), tokenPrice.price);
        setFeeValue(feeValue);
      }
    };
    handleFeeValue();
  }, [rawFee, tokenPrices, currentPaymentToken]);

  // 检查是否是 ICP 地址
  const isICPAddress = (address: string) => {
    return address.includes('-'); // ICP 地址包含连字符
  };


  useEffect(() => {
    const createTx = async () => {
      console.log('create tx....');
      let tx;


      // 如果是 ICP 地址，不创建 Solana 交易
      if (isICPAddress(orderInfo!.merchantSolanaAddress)) {
        console.log(
          'Merchant address is ICP format, skipping Solana tx creation',
        );
        return;
      }

      if (currentUserToken?.type == 'native') {
        tx = await createTransferSOLTx(
          userInfo!.solanaAddress,
          orderInfo!.merchantSolanaAddress,
          Number(paymentInfo!.payTokenAmount),
        );
      } else {
        try {
          // 安全地处理 payTokenAmount 转换为 BigInt
          const payTokenAmountStr = (paymentInfo?.payTokenAmount || '0').toString().trim();

          console.log('Processing payTokenAmount:', {
            original: paymentInfo?.payTokenAmount,
            processed: payTokenAmountStr,
            type: typeof paymentInfo?.payTokenAmount
          });

          // 验证字符串是否为有效数字
          if (!/^\d+$/.test(payTokenAmountStr)) {
            throw new Error(`Invalid payTokenAmount format: "${payTokenAmountStr}"`);
          }

          const amount = BigInt(payTokenAmountStr);

          console.log('Creating SPL transfer with params:', {
            from: userInfo!.solanaAddress,
            to: orderInfo!.merchantSolanaAddress,
            tokenAddress: currentUserToken!.address,
            amount: amount.toString(),
            decimals: currentUserToken!.decimals,
          });

          tx = await createTransferSPLTx(
            userInfo!.solanaAddress,
            orderInfo!.merchantSolanaAddress,
            currentUserToken!.address,
            amount,
            currentUserToken!.decimals,
          );

          if (!tx) {
            throw new Error('Failed to create transaction');
          }

          setRawTx(tx);
          const fee = await tx.getEstimatedFee(solanaClient!);
          setRawFee(fee ? fee + '' : '');
        } catch (error) {
          console.warn('Error creating SPL transaction:', error);
          // 不显示错误提示，因为这是预期的行为
        }
      }
    };
    if (
      currentPaymentToken != undefined &&
      currentUserToken != undefined &&
      orderInfo != undefined &&
      paymentInfo != undefined &&
      userInfo != undefined &&
      solanaClient != undefined
    ) {
      if (orderInfo.paymentStatus === 'success') {
        Toast.show({
          type: 'info',
          text1: t('paymentTitle'),
          text2: t('orderPaymentStatusHint'),
          position: Platform.OS === 'android' ? 'top' : 'bottom',
        });
        navigation.pop();
        return;
      }
      createTx();
    }
  }, [
    orderId,
    currentPaymentToken,
    currentUserToken,
    orderInfo,
    paymentInfo,
    userInfo,
    solanaClient,
  ]);

  const isValidBlanace = useCallback(() => {
    if (
      orderInfo !== undefined &&
      currentUserToken !== undefined &&
      paymentInfo !== undefined
    ) {
      if (currentUserToken.type == 'native') {
        const amount =
          Number(paymentInfo!.payTokenAmount) * LAMPORTS_PER_SOL +
          Number(rawFee);
        return BigNumber(currentUserToken.amount ?? '0').isGreaterThanOrEqualTo(
          BigNumber(amount),
        );
      } else {
        const amount = BigNumber(
          parseUnits(paymentInfo!.payTokenAmount, currentUserToken!.decimals),
        );
        const solTokenAccount = userTokens.find(
          item => item.symbol.toLowerCase() == 'SOL',
        );
        if (solTokenAccount == undefined) {
          return false;
        }
        return (
          BigNumber(solTokenAccount.amount ?? '0').isGreaterThanOrEqualTo(
            BigNumber(rawFee),
          ) &&
          BigNumber(currentUserToken.amount ?? '0').isGreaterThanOrEqualTo(
            amount,
          )
        );
      }
    }
  }, [rawFee, currentUserToken, orderInfo, paymentInfo, userTokens]);


  // 保持原有的 handleTransfer 方法不变
  const handleTransfer = async () => {
    if (overLimit) {
      startKYC();
      return;
    }
    if (loading) {
      return;
    }
    setPaying(true);
    try {

      // 2. 验证通过后再进行转账
      if (
        paymentInfo?.payTokenAmount &&
        paymentInfo?.payTokenDecimal &&
        currentUserToken?.address &&
        orderInfo?.merchantSolanaAddress
      ) {
        // 安全地处理 payTokenAmount 转换为 BigInt
        const payTokenAmountStr = (paymentInfo?.payTokenAmount || '0').toString().trim();

        console.log('Processing payTokenAmount in handleTransfer:', {
          original: paymentInfo?.payTokenAmount,
          processed: payTokenAmountStr,
          type: typeof paymentInfo?.payTokenAmount
        });

        // 验证字符串是否为有效数字
        if (!/^\d+$/.test(payTokenAmountStr)) {
          throw new Error(`Invalid payTokenAmount format: "${payTokenAmountStr}"`);
        }

        const amountUnits = BigInt(payTokenAmountStr);

        const afterTransfer = async (txHashString: string, timestamp: number) => {
          let updateRes: Order | null = null;
          // 轮询订单信息，直到 paymentStatus 字段不等于‘new’
          const pollOrderStatus = async (): Promise<Order | null> => {
            let attempts = 0;
            const maxAttempts = 20; // 最多轮询20次，即60秒

            while (attempts < maxAttempts) {
              try {
                const orderResult = await getOrderInfoById(externalTransactionId);
                console.log(`Polling attempt ${attempts + 1}, order status:`, orderResult?.paymentStatus);

                if (orderResult && orderResult.paymentStatus !== 'new') {
                  console.log('Order status changed from "new" to:', orderResult.paymentStatus);
                  return orderResult;
                }

                // 如果状态仍然是'new'，等待3秒后继续轮询
                if (attempts < maxAttempts - 1) {
                  await new Promise(resolve => setTimeout(resolve, 3000));
                }
                attempts++;
              } catch (error) {
                console.warn(`Polling attempt ${attempts + 1} failed:`, error);
                attempts++;
                if (attempts < maxAttempts) {
                  await new Promise(resolve => setTimeout(resolve, 3000));
                }
              }
            }

            console.log('Polling timeout: order status still "new" after maximum attempts');
            return null;
          };

          updateRes = await pollOrderStatus();
          console.log('Final polling result:', updateRes);

          if (updateRes?.paymentStatus === 'success') {
            console.log('Preparing receipt');
            const receipt = {
              signature: txHashString,
              toAddress: orderInfo!.merchantSolanaAddress,
              merchantName: orderInfo!.merchantName,
              tokenSymbol: paymentInfo!.payTokenSymbol,
              fees: '0',
              timestamp: timestamp,
              amount: paymentInfo!.payTokenAmount,
              transaction_total: updateRes.transaction_total,
              transaction_limit: updateRes.transaction_limit,
              orderValue: orderInfo!.orderValue,
              orderId: orderId,
            };

            console.log('Calling onReceipt with:', receipt);
            onReceipt(receipt);
          }
        }

        // 判断是否是 ICP 地址
        if (isICPAddress(orderInfo.merchantSolanaAddress)) {
          const result = await icrc1TokenTransfer(
            currentUserToken?.address,
            orderInfo?.merchantSolanaAddress,
            amountUnits,
          );
          if (result && 'Ok' in result) {
            console.log('Transfer successful, updating order info');
            const timestamp = new Date().getTime();
            const txHashString = result.Ok.toString();

            console.log('Updating order payment info');

            const principalId = userInfo?.principalId;
            const userId = storage.getNumber('appUserId');
            const getCoinPriceExchange = storage.getString('getCoinPrice');
            console.log(getCoinPriceExchange);
            console.log('getCoinPriceExchange=============');
            afterTransfer(txHashString, timestamp);
          } else {
            Toast.show({
              type: 'error',
              text1: t('send1'),
              text2: stringifyWithBigInt(result),
              position: Platform.OS === 'android' ? 'top' : 'bottom',
            });
          }
        } else {
          // 发送 Solana 交易
          if (!rawTx) {
            throw new Error('Transaction not created');
          }

          console.log('Signing Solana transaction...');
          const signedTx = await signTx(rawTx);

          console.log('Sending Solana transaction...');
          const txHashString = await sendRawTx(signedTx);

          console.log('Solana transaction sent successfully:', txHashString);
          const timestamp = new Date().getTime();

          // 调用成功回调
          await afterTransfer(txHashString, timestamp);
        }

      }
    } catch (error: any) {
      console.warn('Transfer error:', {
        error: error.toString(),
        stack: error.stack,
      });
      Toast.show({
        type: 'error',
        text1: t('send'),
        text2: error.toString(),
        position: Platform.OS === 'android' ? 'top' : 'bottom',
      });
    } finally {
      setPaying(false);
    }
  };

  const overLimit = useMemo(() => {
    return (
      orderInfo?.transaction_limit &&
      orderInfo?.transaction_limit > 0 &&
      (orderInfo.transaction_total || 0) >= orderInfo.transaction_limit
    );
  }, [orderInfo]);

  return (
    <>
      <PaymentCardLayout
        titleContainer={
          <View style={styles.titleContainer}>
            <Text style={styles.titleText}>Confirmation</Text>
          </View>
        }
        merchantName={orderInfo?.merchantName}
        currencyAmount={(Number(orderInfo?.orderValue) / 100).toFixed(2)}
        cryptoAmount={formatUnits(
          (paymentInfo?.payTokenAmount || "0"),
          paymentInfo?.payTokenDecimal || 0
        ) || '0'}
        cryptoSymbol={paymentInfo?.payTokenSymbol || 'vUSD'}
        orderId={orderInfo?.orderId || '--'}
        buttonText="Confirm"
        onButtonPress={handleTransfer}
        buttonIcon={
          paying || loading ? (
            <ActivityIndicator size="small" color={primaryColor} />
          ) : (
            <FaceIcon width={20} height={20} fill="#000000" />
          )
        }
        loading={paying || loading}
        buttonDisabled={paying || loading}
        backgroundColor={primaryBackgroundColor}
        cardBackgroundColor={'#1C1C1D'}
      />

      {currentUserToken ? (
        <SwitchTokenModal
          tokenList={userTokens}
          selectToken={currentUserToken!}
          setSelectToken={value => {
            handleSelectToken(value);
          }}
        />
      ) : null}
    </>
  );
}

// Styles are now handled by PaymentCardLayout component

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
    marginLeft: 8,
  },
});
