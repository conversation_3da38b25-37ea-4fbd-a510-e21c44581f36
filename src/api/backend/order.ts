import {POS_SCAN_UNI_URL} from '../../config';
import {CoinCalculator, Order} from '../../types/order';

export async function getPaynowTransactionInfo({
  externalTransactionId,
}: {
  externalTransactionId: string;
}): Promise<Order | null> {
  try {
    const response = await fetch(
      POS_SCAN_UNI_URL +
        '/transaction/info?externalTransactionId=' +
        externalTransactionId,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const resObj = await response.json();
    if (resObj['code'] == '200') {
      return resObj.data;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('getPaynowTransactionInfo', error);
  }
  return null;
}

export async function getOrderById(orderId: string): Promise<Order> {
  const res = await fetch(
    `${POS_SCAN_UNI_URL}/transaction/info?id=${orderId}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
  const resObj = await res.json();

  if (resObj.code === 200 && resObj.data) {
    return resObj.data;
  } else {
    throw new Error(resObj.message);
  }
}
