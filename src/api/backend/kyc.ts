import {UserProfile} from '../../types/user';
import Config from 'react-native-config';
import {
  PaymentInfo,
  PaymentOrder,
  SupportToken,
  UpdateOrderInfo,
} from '../../types/order';
import {conversionJsonToQueryString, parseValue} from '../../utils';
import {POS_SCAN_UNI_URL} from '../../config';

// 获取 Sumsub SDK Token
export const getSumsubToken = async (authToken: string): Promise<string> => {
  console.log('getSumsubToken', authToken);
  const response = await fetch(
    Config.NODEJS_BACKEND_HOST + '/api/kyc/sumsub_token',
    {
      method: 'POST',
      headers: {
        'auth-token': authToken,
        'Content-Type': 'application/json',
      },
    },
  );
  const resObj = await response.json();
  if (resObj['code'] == '200') {
    return resObj['data'].token || '';
  } else {
    console.warn('getSumsubToken failed', resObj);
    return '';
  }
};

// 获取用户资料(包括KYC验证状态)
export const getUserProfile = async (
  authToken: string,
): Promise<UserProfile | null> => {
  try {
    const response = await fetch(Config.NODEJS_BACKEND_HOST + '/api/user', {
      method: 'GET',
      headers: {
        'auth-token': authToken,
      },
    });
    const resObj = await response.json();

    console.log('getUserProfile resObj:', resObj);
    if (resObj['code'] == '200') {
      return resObj['data'];
    } else {
      console.warn('getUserProfile failed', resObj);
      return null;
    }
  } catch (error) {
    console.log('getUserProfile error:', error);
    return null;
  }
};

// 根据id获取订单
export const getOrderById = async (
  authToken: string,
  id: string,
): Promise<PaymentOrder | null> => {
  console.log('my token:', authToken);
  const response = await fetch(
    Config.NODEJS_BACKEND_HOST +
      '/api/zcloak-rest/uniwebOrder/selectUniwebOrder?id=' +
      id,
    {
      method: 'GET',
      headers: {
        'auth-token': authToken,
      },
    },
  );
  const resObj = await response.json();

  console.log('new getOrderById:', JSON.stringify(resObj));
  if (resObj['code'] == '200') {
    const data = resObj['data'];
    const tokenList = data['storeCryptoMethodDtoList'];
    const supportTokenList: SupportToken[] = [];
    for (let i = 0; i < tokenList.length; i++) {
      const token = tokenList[i];
      supportTokenList.push({
        symbol: token['cryptoSymbol'],
        isNative: (token['cryptoSymbol'] as string).toUpperCase() == 'SOL',
        address: token['cryptoAddress'],
        // address:'5i77q-xjo7v-pvksq-deycb-dgobd-j4z47-4hia4-2ccnc-xq64a-jqypg-gqe'
      });
    }

    const orderInfo: PaymentOrder = {
      orderId: id,
      orderValue: parseValue(Number(data['amount']) + '', 2),
      currency: data['currency'],
      secondVerification: data['secondVerification'] != 'NO',
      merchantName: data['storeName'],
      paymentStatus: data['paymentStatus'],
      merchantSolanaAddress: data['vusdAddress'],
      defaultPaymentToken: supportTokenList[1].symbol,
      supportTokenList: supportTokenList,
      transaction_total: Number(data['transaction_total']),
      transaction_limit: Number(data['transaction_limit']),
    };
    console.log('orderInfo', JSON.stringify(orderInfo));
    return orderInfo;
  } else {
    return null;
  }
};

export async function updateOrder(
  authToken: string,
  params: UpdateOrderInfo,
): Promise<any> {
  try {
    const response = await fetch(
      Config.NODEJS_BACKEND_HOST + '/api/zcloak-rest/uniwebOrder/updateOrder',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify(params),
      },
    );
    const resObj = await response.json();
    console.log('updateOrder resObj:', JSON.stringify(resObj));
    if (resObj['code'] == '200') {
      return resObj.data;
    } else {
      return null;
    }
  } catch (error) {
    console.log('updateOrder error', error);
  }
  return null;
}

export interface IGetPosScanOrderParams {
  limit: number;
  payerWallet: string;
  timestamp?: number;
}

export const getPosScanOrder = async (
  params: IGetPosScanOrderParams,
  authToken: string,
) => {
  try {
    const stringParams = Object.fromEntries(
      Object.entries(params).map(([k, v]) => [k, v?.toString() ?? '']),
    );

    const response = await fetch(
      `${POS_SCAN_UNI_URL}/transaction/orders_by_payer_wallet${conversionJsonToQueryString(
        stringParams,
      )}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
      },
    );
    const result = await response.json();
    if (response.status == 200) {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('getPosScanOrder', error);
  }

  return null;
};

export const forceUpdateOrder = async (
  authToken: string,
  params: {orderId: string},
) => {
  try {
    const response = await fetch(
      POS_SCAN_UNI_URL + '/transaction/force_success',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken,
        },
        body: JSON.stringify(params),
      },
    );
    const resObj = await response.json();
    if (resObj['code'] == '200') {
      return resObj.data;
    } else {
      return null;
    }
  } catch (error) {
    console.warn('forceUpdateOrder', error);
  }
  return null;
};
